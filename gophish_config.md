# Configuration GoPhish - Campagne Formation Sécurité La Turbine

## 📧 EMAIL TEMPLATE

### Nom du template
`Formation_Securite_LaTurbine_2025`

### Expéditeur
- **Nom d'affichage :** MyTraining
- **Adresse email :** <EMAIL>
- **Objet :** Invitation : Formation sécurité — 28/09/2025 — confirmation obligatoire

### Contenu
- **HTML :** Utiliser le fichier `email_template.html` (logo La Turbine2.png intégré)
- **Texte :** Utiliser le fichier `email_template_text.txt`
- **Logo :** Le fichier `La Turbine2.png` doit être accessible depuis le serveur web

### Pièce jointe
- **Nom du fichier :** `Invit_Formation_Securite_28-09-2025.pdf`
- **Type :** PDF (à créer séparément)
- **Contenu suggéré :** Badge d'accès factice avec logo La Turbine

---

## 🎯 LANDING PAGE

### Nom de la page
`Confirmation_Formation_LaTurbine`

### URL suggérée
`https://formation-securite.laturbine-cergypontoise.fr/confirmation`
ou
`https://laturbine-cergypontoise.fr/formation/confirmation`

### Contenu
- **HTML :** Utiliser le fichier `landing_page_auth.html` (recommandé)
- **Logo :** Le fichier `La Turbine2.png` doit être accessible depuis le serveur web
- **Redirection après soumission :** `https://laturbine-cergypontoise.fr/`
- **Délai de redirection :** 2 secondes

### Données collectées
- Prénom
- Nom  
- Email professionnel
- Mot de passe
- Service/Département
- Site de travail
- Téléphone professionnel

---

## 👥 GROUPES CIBLES

### Nom du groupe
`Responsables_LaTurbine_2025`

### Cibles suggérées
- Direction générale
- Responsables de service
- Chargés de projet
- Responsables techniques
- Coordinateurs résidents

### Format CSV pour import
```csv
First Name,Last Name,Email,Position
[Prénom],[Nom],[<EMAIL>],[Poste]
```

---

## 📊 SENDING PROFILE

### Nom du profil
`SMTP_LaTurbine_Training`

### Configuration SMTP
- **Serveur SMTP :** À configurer selon votre infrastructure
- **Port :** 587 (STARTTLS) ou 465 (SSL)
- **Authentification :** Oui
- **From Address :** <EMAIL>

---

## 🚀 CAMPAGNE

### Nom de la campagne
`Formation_Securite_Sept2025_Sensibilisation`

### Paramètres
- **Template :** Formation_Securite_LaTurbine_2025
- **Landing Page :** Confirmation_Formation_LaTurbine
- **URL :** À définir selon votre domaine de test
- **Sending Profile :** SMTP_LaTurbine_Training
- **Groups :** Responsables_LaTurbine_2025

### Timing
- **Date de lancement :** À définir
- **Envoi immédiat :** Recommandé pour simulation
- **Suivi :** 7 jours minimum

---

## 🔍 MÉTRIQUES À SURVEILLER

1. **Taux d'ouverture** - Emails ouverts
2. **Taux de clic** - Clics sur le lien de confirmation  
3. **Taux de soumission** - Formulaires complétés
4. **Données sensibles** - Mots de passe saisis
5. **Temps de réaction** - Délai entre envoi et action

---

## 🛡️ RECOMMANDATIONS SÉCURITÉ

1. **Domaine de test :** Utiliser un domaine similaire mais distinct
2. **Certificat SSL :** Obligatoire pour la crédibilité
3. **Logs :** Activer tous les logs pour analyse
4. **Nettoyage :** Supprimer les données collectées après analyse
5. **Débriefing :** Prévoir une session de sensibilisation post-campagne

---

## 📋 CHECKLIST PRE-LANCEMENT

- [ ] Template email testé et validé
- [ ] Landing page fonctionnelle
- [ ] Logo La Turbine2.png uploadé sur le serveur web
- [ ] Redirection vers site légitime configurée
- [ ] Pièce jointe PDF créée
- [ ] Liste des cibles validée
- [ ] SMTP configuré et testé
- [ ] Domaine de test configuré
- [ ] Certificat SSL installé
- [ ] Plan de débriefing préparé
