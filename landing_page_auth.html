<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accès Formation Sécurité | La Turbine Cergy-Pontoise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f4f4f4;
        }
        
        .header-bar {
            background: #333;
            color: white;
            padding: 10px 0;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-links a {
            color: #f39c12;
            text-decoration: none;
            padding: 8px;
        }
        
        .main-header {
            background: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: #2dd4bf;
            border-radius: 50% 0 50% 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #2dd4bf 0%, #0891b2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .hero-content h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .main-content {
            max-width: 500px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .auth-card {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .auth-header {
            margin-bottom: 30px;
        }
        
        .auth-header h2 {
            color: #2dd4bf;
            margin-bottom: 15px;
        }
        
        .security-notice {
            background: #e7f3ff;
            border-left: 4px solid #2dd4bf;
            padding: 20px;
            margin: 25px 0;
            text-align: left;
            border-radius: 0 8px 8px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #2dd4bf;
            box-shadow: 0 0 0 3px rgba(45, 212, 191, 0.1);
        }
        
        .login-btn {
            background: #2dd4bf;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
            margin-top: 10px;
        }
        
        .login-btn:hover {
            background: #0891b2;
        }
        
        .help-text {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .help-text a {
            color: #2dd4bf;
            text-decoration: none;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 60px;
        }
        
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .auth-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header-bar">
        <div class="header-content">
            <div class="social-links">
                <a href="#">in</a>
                <a href="#">📷</a>
            </div>
            <div style="color: #ccc; font-size: 14px;">
                Espace Formation Sécurité
            </div>
        </div>
    </div>
    
    <div class="main-header">
        <div class="nav-content">
            <div class="logo-section">
                <img src="La Turbine2.png" alt="La Turbine Cergy-Pontoise" style="max-width: 200px; height: auto;">
            </div>
        </div>
    </div>
    
    <div class="hero-section">
        <div class="hero-content">
            <h1>🔒 Accès Sécurisé</h1>
            <p>Formation Sécurité 2025 - Espace Collaborateurs</p>
        </div>
    </div>
    
    <div class="main-content">
        <div class="auth-card">
            <div class="auth-header">
                <h2>Authentification requise</h2>
                <p>Veuillez vous connecter pour accéder aux détails de la formation et confirmer votre présence.</p>
            </div>
            
            <div class="security-notice">
                <strong>🔐 Sécurité renforcée</strong><br>
                Conformément aux nouvelles directives de sécurité, l'accès aux informations de formation nécessite une authentification avec vos identifiants La Turbine.
            </div>
            
            <form id="authForm" action="#" method="POST">
                <div class="form-group">
                    <label for="email">Adresse email professionnelle</label>
                    <input type="email" id="email" name="email" required 
                           placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="Votre mot de passe La Turbine">
                </div>
                
                <button type="submit" class="login-btn">
                    🔓 Accéder à la formation
                </button>
            </form>
            
            <div class="help-text">
                <p>Problème de connexion ? <a href="#">Contactez le support IT</a></p>
                <p style="margin-top: 10px; font-size: 12px;">
                    Cette authentification est requise pour des raisons de sécurité et de conformité RGPD.
                </p>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><strong>La Turbine Cergy-Pontoise</strong></p>
        <p>Formation Sécurité 2025 - MyTraining</p>
        <p style="margin-top: 15px; font-size: 14px; color: #ccc;">
            Support technique : <EMAIL>
        </p>
    </div>
    
    <script>
        document.getElementById('authForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const loginBtn = document.querySelector('.login-btn');
            const originalText = loginBtn.innerHTML;
            
            loginBtn.innerHTML = '⏳ Vérification...';
            loginBtn.disabled = true;
            
            // Collecte des identifiants
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Simulation d'authentification
            setTimeout(() => {
                loginBtn.innerHTML = '✅ Connexion réussie';
                
                // Message de redirection
                setTimeout(() => {
                    const authCard = document.querySelector('.auth-card');
                    authCard.innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <h3 style="color: #2dd4bf; margin-bottom: 20px;">✅ Authentification réussie</h3>
                            <p>Redirection vers l'espace formation...</p>
                            <div style="margin: 20px 0;">
                                <div style="width: 100%; height: 4px; background: #f0f0f0; border-radius: 2px; overflow: hidden;">
                                    <div style="width: 0%; height: 100%; background: #2dd4bf; animation: progress 3s ease-in-out forwards;"></div>
                                </div>
                            </div>
                        </div>
                        <style>
                            @keyframes progress {
                                to { width: 100%; }
                            }
                        </style>
                    `;
                    
                    // Redirection vers le vrai site
                    setTimeout(() => {
                        window.location.href = 'https://laturbine-cergypontoise.fr/';
                    }, 3000);
                }, 1000);
            }, 2000);
        });
    </script>
</body>
</html>
