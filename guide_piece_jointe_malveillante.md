# 🦠 Guide - Pièce jointe malveillante pour laboratoire

## 🎯 **Objectif pédagogique**
Créer une pièce jointe réaliste avec payload éducatif pour démontrer les techniques d'attaque par email dans un environnement de laboratoire contrôlé.

---

## 📋 **Options de payload disponibles**

### **Option 1 : PDF avec JavaScript** ⭐ **RECOMMANDÉ**
```
✅ Avantages :
- Plus subtil et réaliste
- Contourne souvent les antivirus
- Ressemble à un vrai document d'entreprise
- Collecte d'informations système

❌ Inconvénients :
- Dépend des paramètres de sécurité PDF
- Peut être bloqué par les lecteurs modernes
```

### **Option 2 : Document Office avec macro**
```
✅ Avantages :
- Très répandu en entreprise
- Techniques bien documentées
- Contrôle total du payload

❌ Inconvénients :
- Avertissements de sécurité visibles
- Nécessite activation manuelle des macros
```

### **Option 3 : Exécutable déguisé**
```
✅ Avantages :
- Contrôle total du système
- Payload personnalisable
- Techniques de camouflage

❌ Inconvénients :
- Détection antivirus élevée
- Moins crédible pour un badge
```

---

## 🔧 **Création du PDF malveillant**

### **Étape 1 : Installation des dépendances**
```bash
pip install reportlab PyPDF2
```

### **Étape 2 : Génération du PDF de base**
```bash
python create_malicious_pdf.py
```

### **Étape 3 : Génération des payloads PowerShell**
```bash
python create_powershell_payload.py
```

### **Étape 4 : Intégration du JavaScript**
Le script génère automatiquement :
- `Badge_Formation_Securite_28-09-2025.pdf` (base)
- `Badge_Formation_Securite_28-09-2025_malicious.pdf` (avec JS)
- `javascript_payload.js` (code JavaScript)

---

## 🎯 **Payloads disponibles**

### **Payload 1 : Collecte d'informations** 🔍
```powershell
# Collecte automatique :
- Nom de l'ordinateur
- Utilisateur connecté
- Système d'exploitation
- Adresse IP et MAC
- Logiciels installés
- Processus en cours
```

### **Payload 2 : Reverse shell** 🔴
```powershell
# Connexion inverse vers votre serveur
# Accès shell complet au système cible
# Configuration : IP + Port de votre choix
```

### **Payload 3 : JavaScript PDF** 📄
```javascript
// Exécution automatique à l'ouverture
// Lancement de PowerShell en arrière-plan
// Message de confirmation trompeur
// Collecte d'informations navigateur
```

---

## 🚀 **Déploiement dans GoPhish**

### **Configuration de l'email**
```
Sujet : Formation Sécurité 2025 - Badge d'accès requis
Pièce jointe : Badge_Formation_Securite_28-09-2025_malicious.pdf
Template : email_template.html (déjà créé)
```

### **Serveur de collecte**
```bash
# Listener pour reverse shell
nc -lvp 4444

# Serveur web pour collecte d'infos
python -m http.server 8080
```

---

## 📊 **Métriques de succès**

### **Indicateurs à surveiller**
- **Taux d'ouverture email** : % qui ouvrent l'email
- **Taux de téléchargement** : % qui téléchargent le PDF
- **Taux d'exécution** : % qui ouvrent le PDF
- **Collecte de données** : Informations système récupérées
- **Connexions reverse** : Shells obtenus

### **Données collectées**
```json
{
  "computerName": "PC-LATURBINE-01",
  "userName": "jean.dupont",
  "domain": "LATURBINE",
  "os": "Windows 10 Pro",
  "ipAddress": "************",
  "timestamp": "2025-01-XX XX:XX:XX"
}
```

---

## 🛡️ **Détection et prévention**

### **Signaux d'alerte à enseigner**
1. **Email inattendu** avec pièce jointe
2. **Demande d'activation** de macros/JavaScript
3. **Fichiers avec double extension** (.pdf.exe)
4. **Messages d'erreur** suspects
5. **Activité réseau** inhabituelle

### **Bonnes pratiques à promouvoir**
- ✅ Vérifier l'expéditeur par un autre canal
- ✅ Scanner les pièces jointes avant ouverture
- ✅ Désactiver JavaScript dans les PDF
- ✅ Utiliser des lecteurs PDF sécurisés
- ✅ Maintenir les logiciels à jour

---

## 🔬 **Analyse post-campagne**

### **Questions à poser aux participants**
1. Avez-vous remarqué quelque chose de suspect ?
2. Qu'est-ce qui vous a convaincu d'ouvrir la pièce jointe ?
3. À quel moment avez-vous réalisé que c'était un test ?
4. Que feriez-vous différemment maintenant ?

### **Points d'apprentissage**
- **Ingénierie sociale** : Techniques de manipulation
- **Vecteurs d'attaque** : Email, pièces jointes, liens
- **Indicateurs de compromission** : Signaux d'alerte
- **Réponse aux incidents** : Que faire en cas de doute

---

## ⚠️ **Considérations éthiques et légales**

### **Prérequis obligatoires**
- ✅ **Autorisation écrite** de la direction
- ✅ **Environnement de laboratoire** isolé
- ✅ **Consentement** des participants
- ✅ **Objectif pédagogique** clairement défini

### **Limites à respecter**
- ❌ Pas d'accès aux données personnelles
- ❌ Pas de modification de fichiers système
- ❌ Pas de propagation sur le réseau
- ❌ Pas d'utilisation malveillante réelle

### **Nettoyage post-test**
- 🧹 Suppression des payloads installés
- 🧹 Nettoyage des logs et traces
- 🧹 Restauration des systèmes modifiés
- 🧹 Débriefing complet avec les participants

---

## 📚 **Ressources complémentaires**

### **Outils recommandés**
- **GoPhish** : Plateforme de phishing
- **Metasploit** : Framework de test de pénétration
- **Social Engineer Toolkit** : Outils d'ingénierie sociale
- **Wireshark** : Analyse du trafic réseau

### **Formation continue**
- Certification en cybersécurité
- Veille sur les nouvelles techniques d'attaque
- Participation à des exercices de simulation
- Mise à jour régulière des connaissances

---

## 🎓 **Conclusion**

Cette approche pédagogique permet de sensibiliser efficacement aux risques du phishing tout en respectant un cadre éthique strict. L'objectif est d'éduquer, pas de nuire.

**Rappel important** : Ces outils sont exclusivement destinés à un usage éducatif dans un environnement de laboratoire contrôlé.
