
$info = @{}
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

$jsonData = $info | ConvertTo-Json -Compress
$jsonData | Out-File -FilePath "$env:TEMP\system_info_laturbine.json"

Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
